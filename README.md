# Front Development Tools MCP

> 基于 Model Context Protocol (MCP) 的前端开发工具集合，提供自动化的分支管理和NPM包升级功能

## 📋 项目概述

本项目是一个专门面向前端开发设计的 MCP 服务器，通过 AI 助手可以自然语言交互的方式执行复杂的开发任务。主要功能包括：

- **🌿 智能分支管理**: 自动创建H5中台化分支，支持多应用联动
- **📦 NPM包自动升级**: 当NPM包发布后，自动更新相关应用的依赖版本
- **🔔 实时通知**: 集成飞书webhook，实时推送操作结果
- **🤖 AI友好**: 支持自然语言指令，无需记忆复杂命令

## ‼️重要声明
> 本项目为公司内部项目，包含敏感的git仓库信息，请勿外传

## 开发说明

### 1. 安装依赖

```bash
make install
```

### 2. 启动

```bash
make dev
```

### 3. 测试

```bash
make test
```

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- Git 环境配置
- 相关仓库访问权限

### 配置环境变量
创建 `.env` 文件并配置以下变量：
```bash
# Git私有token (必需)
# 用于访问GitLab API，进行分支创建、文件更新等操作
private_token=your_git_private_token

# 项目目录 (必需)
# 本地存储git仓库的绝对路径，用于克隆和管理多个应用仓库
# 例如：/Users/<USER>/Documents/code 或 /home/<USER>/projects
app_dir=/path/to/your/projects/directory

# 飞书webhook地址 (可选，用于通知)
# 用于发送操作结果通知到飞书群聊
webhook_url=https://open.feishu.cn/open-apis/bot/v2/hook/your_webhook_id

# 服务端口 (可选，默认3002)
PORT=3002
```

**重要说明**:
- `app_dir`: 这是一个**必需**的环境变量，指定本地存储所有git仓库的根目录
  - 系统会在此目录下自动创建和管理各个应用的子目录
  - 如果应用目录不存在，系统会自动从GitLab克隆相应的仓库
  - 目录结构示例：
    ```
    /your/app/dir/
    ├── wsc-tee-h5/          # H5应用仓库
    ├── wsc/                 # WSC应用仓库
    ├── wsc-pc-trade/        # PC交易应用仓库
    ├── retail-node-order/   # 订单服务仓库
    └── retail-node-fulfillment/ # 履约服务仓库
    ```

### 环境变量配置示例
```bash
# .env 文件示例
PORT=3002
private_token=glpat-xxxxxxxxxxxxxxxxxxxx
app_dir=/Users/<USER>/Documents/projects
webhook_url=https://open.feishu.cn/open-apis/bot/v2/hook/xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
```

## 📱 MCP客户端配置

### 方式一: SSE连接 (推荐用于开发)
> 需要本地启动服务

```json
{
  "mcpServers": {
    "front-development-tools-mcp": {
      "timeout": 1000000,
      "url": "http://localhost:3002/sse"
    }
  }
}
```

### 方式二: NPX直接启动 (推荐用于生产)

```json
{
  "mcpServers": {
    "front-development-tools-mcp": {
      "timeout": 1000000,
      "command": "npx",
      "args": [
        "-y",
        "front-development-tools-mcp",
        "--stdio"
      ],
      "env": {
        "private_token": "your_git_private_token",
        "webhook_url": "your_webhook_url",
        "app_dir": "/path/to/your/projects/directory"
      }
    }
  }
}
```

## 🛠️ 核心功能

本项目提供 **3个核心MCP工具**，支持完整的前端开发自动化流程：

### 1. H5中台化分支管理 (`create_ranta_h5_branch`)

**功能描述**: 智能创建H5中台化分支，支持多应用联动和依赖关系管理

**支持的业务模块**:
```
ext-tee-wsc-decorate, ext-tee-wsc-decorate-h5, ext-tee-wsc-statcenter,
ext-tee-assets, ext-tee-passport, ext-tee-shop, ext-tee-wsc-goods,
ext-tee-wsc-ump, ext-tee-salesman, ext-tee-logger, ext-tee-wsc-im,
ext-tee-wsc-trade, ext-tee-cps, ext-tee-retail-prepaid, ext-tee-guide,
ext-tee-navigate, ext-tee-edu-goods, ext-tee-retail-groupbuy,
ext-tee-retail-solitaire, ext-tee-wholesale, ext-tee-common,
ext-tee-user, ext-tee-retail-shelf, ext-tee-live
```

**AI交互示例**:
- 🗣️ **用户**: "创建分支 hotfix/jira-9014，包含 trade"
- ⚙️ **执行过程**:
  1. 自动创建 `ext-tee-wsc-trade`、`wsc-tee-h5`、`wsc` 三个应用的分支
  2. 更新 `wsc-tee-h5` 和 `wsc` 的 `koko.repo.json` 文件中的依赖分支
  3. 推送代码并发送飞书通知

### 2. NPM包自动升级系统

**功能描述**: NPM包发布后自动更新相关应用的依赖版本，采用用户指定应用的方式，支持灵活的应用选择

**工作流程**:
1. **第一阶段** (`npm_publish_upgrade_app`): 参数验证，支持一步完成或两步完成
2. **第二阶段** (`npm_publish_upgrade_app_running`): 当未在第一阶段指定应用时，执行具体的升级操作

**使用方式**:

**方式一：一步完成（推荐）**
在第一个工具中直接指定目标应用列表，一次性完成升级：
- 提供 `apps` 参数：逗号分隔的应用名称，如 `wsc-tee-h5,wsc`
- 可选 `clientDir` 参数：指定 package.json 所在目录，如 `client`

**方式二：两步完成**
先验证参数，再指定应用：
1. 第一步：只提供包名、版本、分支，不提供 `apps` 参数
2. 第二步：系统提示后，使用第二个工具指定应用列表

**子目录支持**:
如果应用的 `package.json` 位于子目录下（如 `client` 目录），可以通过 `clientDir` 参数指定：
- `clientDir: "client"` - package.json 在 client 目录下
- `clientDir: "frontend"` - package.json 在 frontend 目录下
- `clientDir: ""` 或不指定 - package.json 在根目录下

**目录结构示例**:
```
your-app-repo/
├── README.md
├── client/              # 前端代码目录
│   ├── package.json     # 需要更新的package.json
│   ├── src/
│   └── ...
└── server/              # 后端代码目录
    └── ...
```

**AI交互示例**:

**场景一：一步完成升级（推荐）**
- 🗣️ **用户**: "发布分支：hotfix/test-0712-2322 包名和版本：@youzan/order-domain-pc-components@1.1.9-beta.20250710151649.0 应用：wsc-pc-trade,retail-node-order,retail-node-fulfillment 目录：client"
- ⚙️ **系统响应**:
  1. 验证参数
  2. 直接执行升级操作
  3. 返回升级完成结果

**场景二：两步完成升级**
- 🗣️ **用户**: "发布分支：feature/new-feature 包名和版本：@youzan/custom-package@1.0.0"
- 🤖 **AI**: "✅ 参数验证通过，npm包升级任务已准备就绪。请告诉我你想要更新哪些应用以及package.json所在目录（不指定默认根目录），多个应用以逗号分隔。例如：需要更新的应用列表：app1,app2,app3；目录：client"
- 🗣️ **用户**: "wsc-tee-h5,wsc"
- ⚙️ **系统响应**: 在指定的应用中执行升级操作

**场景三：升级失败重试**
- 🗣️ **用户**: "重新执行升级"
- ⚙️ **系统响应**: 使用之前保存的参数重新执行升级操作

### 3. 工具详细说明

#### 3.1 `npm_publish_upgrade_app`
**作用**: 参数验证和升级执行的主要工具
- 验证npm包名、版本和分支参数
- 支持两种使用方式：
  - **一步完成**: 提供 `apps` 参数直接执行升级
  - **两步完成**: 不提供 `apps` 参数，保存数据等待后续指定
- 支持 `clientDir` 参数指定 package.json 所在目录

#### 3.2 `npm_publish_upgrade_app_running`
**作用**: 执行具体升级操作的第二阶段工具（仅在两步完成模式下使用）
- 支持指定具体应用列表（逗号分隔）
- 支持指定客户端目录路径
- 处理升级失败的重试场景
- 使用第一阶段保存的参数执行升级


## 🔧 技术架构

### 核心技术栈
- **MCP SDK**: `@modelcontextprotocol/sdk` - Model Context Protocol 实现
- **TypeScript**: 类型安全的开发体验
- **Express**: HTTP服务器和SSE支持
- **Git操作**: 自动化的分支管理和代码提交
- **GitLab API**: 远程仓库操作和文件更新
- **本地仓库管理**: 基于 `app_dir` 的多仓库本地缓存
- **Webhook集成**: 飞书通知推送

### 工作原理
1. **仓库管理**: 基于 `app_dir` 环境变量管理本地仓库缓存
   - 首次使用时自动从GitLab克隆仓库到本地
   - 后续操作复用本地仓库，提高执行效率
   - 支持多个应用仓库的并行管理

2. **混合操作模式**: 结合GitLab API和本地Git命令
   - 使用GitLab API进行远程分支创建和文件更新
   - 使用本地Git命令进行代码同步和依赖安装
   - 确保操作的可靠性和一致性

### 项目结构
```
src/
├── cli.ts              # CLI入口和服务器创建
├── server.ts           # HTTP/SSE服务器实现
├── config.ts           # 配置管理
├── mcp-tools/          # MCP工具注册
│   ├── create-branch.ts    # 分支创建工具
│   ├── npm-publish.ts      # NPM升级工具
│   └── index.ts           # 工具注册入口
├── packages/           # 核心业务逻辑
│   ├── create-branch/     # 分支管理功能
│   └── npm-publish/       # NPM升级功能
└── utils/              # 工具函数
    ├── logger.ts          # 日志系统
    ├── webhook.ts         # 通知推送
    └── memory-store.ts    # 内存存储
```

## 🚀 部署运行

### 后台持久运行 (PM2)

1. **安装PM2**
```bash
npm install -g pm2
```

2. **启动服务**
```bash
pm2 start make --name "front-development-tools-mcp" -- dev
```

3. **查看运行状态**
```bash
pm2 list
pm2 logs front-development-tools-mcp
```

4. **进程管理**
```bash
# 重启服务
pm2 restart front-development-tools-mcp

# 停止服务
pm2 stop front-development-tools-mcp

# 删除进程
pm2 delete front-development-tools-mcp
```

5. **开机自启**
```bash
pm2 save
pm2 startup
# 执行输出的命令
```

### Docker部署 (可选)
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 3002
CMD ["npm", "start"]
```

## 🤖 AI助手使用示例

### 创建分支示例
```
用户: "帮我创建一个分支 hotfix/fix-trade-bug，包含 trade 和 goods 业务"
AI: 正在为您创建分支...
✅ 成功创建以下分支：
- ext-tee-wsc-trade: hotfix/fix-trade-bug
- ext-tee-wsc-goods: hotfix/fix-trade-bug
- wsc-tee-h5: hotfix/fix-trade-bug
- wsc: hotfix/fix-trade-bug
并已更新相关依赖配置文件
```

### NPM升级示例

**一步完成场景**:
```
用户: "发布分支：feature/new-feature 包名和版本：@youzan/order-domain-pc-components@2.1.0 应用：wsc-pc-trade,retail-node-order,retail-node-fulfillment 目录：client"
AI: ✅ npm包升级任务执行完成

目标应用: wsc-pc-trade, retail-node-order, retail-node-fulfillment
升级包: @youzan/order-domain-pc-components@2.1.0
分支: feature/new-feature
客户端目录: client

详细结果: [升级操作详情]
```

**两步完成场景**:
```
用户: "发布分支：hotfix/custom-fix 包名和版本：@youzan/custom-utils@1.5.0"
AI: ✅ 参数验证通过，npm包升级任务已准备就绪

升级包: @youzan/custom-utils@1.5.0
分支: hotfix/custom-fix

请告诉我你想要更新哪些应用以及package.json所在目录（不指定默认根目录），多个应用以逗号分隔。例如：需要更新的应用列表：app1,app2,app3；目录：client

用户: "wsc-tee-h5,wsc"
AI: ✅ npm包升级任务执行完成

目标应用: wsc-tee-h5, wsc
升级包: @youzan/custom-utils@1.5.0
分支: hotfix/custom-fix

详细结果: [升级操作详情]
```

**⚠️ 注意**: 本项目包含公司内部敏感信息，请确保在安全的环境中使用，不要将配置信息泄露到公共环境。

