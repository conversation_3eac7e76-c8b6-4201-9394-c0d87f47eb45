import taskWithCustomApps from "./task-custom.js";
import { notify } from "../../utils/webhook.js"; // 引入通知函数
import { generateNotificationText } from "./notification.js";
import { Logger } from "~/utils/logger.js";

/**
 * 自定义应用列表的npm包升级主函数
 * @param mcpOptions 升级选项
 * @param targetApps 目标应用列表
 */
async function mainWithCustomApps(mcpOptions: any, targetApps: string[]) {
  // 格式转化
  const publishConfig = {
    branch_name: mcpOptions.branch,
    modules: [
      {
        module_name: mcpOptions.npmName,
        version: mcpOptions.npmVersion,
      },
    ],
  };

  // 创建自定义的应用映射
  const customAppMaps = {
    [mcpOptions.npmName]: targetApps,
  };

  try {
    Logger.log("Received npm publish data:", publishConfig);
    Logger.log("Target apps:", targetApps);
    
    const result = await taskWithCustomApps(publishConfig, customAppMaps, mcpOptions.clientDir || '');
    Logger.log("NPM upgrade result:", result);

    // 生成通知文本
    const notificationText = generateNotificationText(result, publishConfig);

    // 发送通知
    notify({
      msg_type: "text",
      content: {
        text: notificationText,
      },
    });
    return notificationText;
  } catch (error: any) {
    console.error("Error processing npm publish:", error);
    const errorText = `❌ NPM包更新处理失败\n错误: ${
          error.message
        }\n数据: ${JSON.stringify(publishConfig)}\n目标应用: ${targetApps.join(', ')}`;
    // 发送错误通知
    notify({
      msg_type: "text",
      content: {
        text: errorText,
      },
    });
    return errorText;
  }
}

export default mainWithCustomApps;
