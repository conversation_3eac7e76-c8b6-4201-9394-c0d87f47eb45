
/**
 * npm包手动更新应用并提交 - 重构版本
 * 第一个工具：参数验证阶段，不再自动匹配应用
 */
import { z } from "zod";
import { Logger } from "~/utils/logger.js";
import memoryStore from "~/utils/memory-store.js";
import mainWithCustomApps from "../packages/npm-publish/main-custom.js";

export default function registerNpmpublishTool(server: any) {
  // 第一个工具：npm_publish_upgrade_app - 参数验证，可选择一步完成或两步完成
  server.tool(
    "npm_publish_upgrade_app",
    "when npm package published, validate parameters and optionally execute upgrade. Can be used in two ways: 1) Provide 'apps' parameter for one-step completion, 2) Skip 'apps' parameter for two-step process.",
    {
      npmName: z
        .string()
        .describe(
          "The npmName of the published package, often found in a provided string like /包名和版本：<npmName>@<npmVersion>/"
        ),
      npmVersion: z
        .string()
        .describe(
          "The npmVersion of the published package, often found in a provided string like /包名和版本：<npmName>@<npmVersion>/"
        ),
      branch: z
        .string()
        .describe(
          "The branch of the published branch, often found in a provided string like /发布分支：<branch>/"
        ),
      clientDir: z
        .string()
        .optional()
        .default('')
        .describe(
          "客户端目录路径，可选参数，默认为空字符串。例如：'client' 或 'src/client'"
        ),
      apps: z
        .string()
        .optional()
        .describe(
          "要更新的应用列表，多个应用以逗号分隔，例如：'app1,app2,app3'。如果不提供，将在验证后提示用户输入"
        ),
    },
    async ({ npmName, npmVersion, branch, clientDir, apps }: any) => {
      Logger.log(
        `Starting npm upgrade process: ${npmName}@${npmVersion} on ${branch}, clientDir: ${clientDir || '(default)'}, apps: ${apps || '(not provided)'}`
      );

      try {
        // 1. 参数验证
        if (!npmName || !npmVersion || !branch) {
          return {
            content: [
              {
                type: "text",
                text: "❌ 参数验证失败：npmName、npmVersion 和 branch 都是必需的参数",
              },
            ],
          };
        }

        // 验证分支不能是master
        if (branch === "master") {
          return {
            content: [
              {
                type: "text",
                text: "❌ 参数验证失败：不允许在 master 分支上进行更新操作",
              },
            ],
          };
        }

        Logger.log(`参数验证通过: ${npmName}@${npmVersion} on ${branch}`);

        // 2. 检查是否提供了 apps 参数
        if (apps && apps.trim()) {
          // 用户已提供应用列表，直接执行升级操作
          Logger.log(`用户已提供应用列表，直接执行升级: ${apps}`);

          // 解析应用列表
          const targetApps = apps.split(',').map((app: string) => app.trim()).filter((app: string) => app);
          if (targetApps.length === 0) {
            return {
              content: [
                {
                  type: "text",
                  text: "❌ 提供的应用列表无效，请确保应用名称以英文逗号分隔且不为空",
                },
              ],
            };
          }

          Logger.log(`解析到的目标应用: ${targetApps.join(', ')}`);

          try {
            // 直接执行升级操作
            const result = await mainWithCustomApps({
              npmName,
              npmVersion,
              branch,
              clientDir,
            }, targetApps);

            Logger.log(`一步完成npm包升级`);

            return {
              content: [
                {
                  type: "text",
                  text: `✅ npm包升级任务执行完成\n\n目标应用: ${targetApps.join(', ')}\n升级包: ${npmName}@${npmVersion}\n分支: ${branch}\n客户端目录: ${clientDir || '(默认)'}\n\n详细结果:\n${result}`,
                },
              ],
            };
          } catch (error: any) {
            Logger.error("一步完成npm包升级失败:", error);
            return {
              content: [
                {
                  type: "text",
                  text: `❌ npm包升级执行失败: ${error.message}\n\n升级包: ${npmName}@${npmVersion}\n目标应用: ${targetApps.join(', ')}\n分支: ${branch}`,
                },
              ],
            };
          }
        } else {
          // 用户未提供应用列表，保存数据到内存并提示用户
          Logger.log(`用户未提供应用列表，保存数据到内存等待后续指定`);

          // 3. 将验证通过的参数保存到内存中，matchedApps 设置为空数组
          const storeKey = `npm_upgrade_${Date.now()}`;
          const upgradeData = {
            npmName,
            npmVersion,
            branch,
            clientDir,
            matchedApps: [], // 不再自动匹配，设置为空数组
            timestamp: new Date().toISOString(),
          };

          memoryStore.set(storeKey, upgradeData);
          memoryStore.set('latest_npm_upgrade', storeKey); // 保存最新的升级任务key

          Logger.log(`数据已保存到内存，key: ${storeKey}`);

          // 4. 提示用户需要指定应用列表
          return {
            content: [
              {
                type: "text",
                text: `✅ 参数验证通过，npm包升级任务已准备就绪\n\n升级包: ${npmName}@${npmVersion}\n分支: ${branch}\n\n请告诉我你想要更新哪些应用以及package.json所在目录（不指定默认根目录），多个应用以逗号分隔。例如：需要更新的应用列表：app1,app2,app3；目录：client`,
              },
            ],
          };
        }
      } catch (error: any) {
        Logger.error("npm_publish_upgrade_app 执行失败:", error);
        return {
          content: [
            {
              type: "text",
              text: `❌ 处理失败: ${error.message}`,
            },
          ],
        };
      }
    }
  );

  // 第二个工具：npm_publish_upgrade_app_running - 手动执行应用更新任务
  server.tool(
    "npm_publish_upgrade_app_running",
    "Manually execute npm package upgrade for specified apps. Use this after the first tool to specify which apps to upgrade.",
    {
      apps: z
        .string()
        .describe(
          "The apps to upgrade, comma-separated app names like app1,app2,app3 "
        ),
      clientDir: z
        .string()
        .optional()
        .default('')
        .describe(
          "客户端目录路径，可选参数，默认为空字符串。如果不提供，将使用第一个工具中保存的值"
        ),
    },
    async ({ apps, clientDir: overrideClientDir }: any) => {
      Logger.log(`开始执行npm包升级任务，目标应用: ${apps}, clientDir: ${overrideClientDir || '(use saved value)'}`);

      try {
        // 1. 获取最新的升级任务数据
        const latestUpgradeKey = memoryStore.get('latest_npm_upgrade');
        if (!latestUpgradeKey) {
          return {
            content: [
              {
                type: "text",
                text: "❌ 未找到待处理的npm升级任务，请先执行 npm_publish_upgrade_app 工具进行参数验证和应用匹配",
              },
            ],
          };
        }

        const upgradeData = memoryStore.get(latestUpgradeKey);
        if (!upgradeData) {
          return {
            content: [
              {
                type: "text",
                text: "❌ 升级任务数据已过期或不存在，请重新执行 npm_publish_upgrade_app 工具",
              },
            ],
          };
        }

        const { npmName, npmVersion, branch, clientDir: savedClientDir } = upgradeData;
        Logger.log(`获取到升级数据: ${npmName}@${npmVersion} on ${branch}`);

        // 确定最终使用的 clientDir：优先使用当前传入的，否则使用保存的值
        const finalClientDir = overrideClientDir !== undefined ? overrideClientDir : (savedClientDir || '');
        Logger.log(`使用的 clientDir: ${finalClientDir || '(default)'}`);

        // 2. 处理用户指定的应用列表（不再支持"全部"选项）
        const targetApps = apps.split(',').map((app: string) => app.trim()).filter((app: string) => app);
        if (targetApps.length === 0) {
          return {
            content: [
              {
                type: "text",
                text: "❌ 未指定有效的应用名称，请提供逗号分隔的应用列表，例如：app1,app2,app3",
              },
            ],
          };
        }
        Logger.log(`用户指定应用: ${targetApps.join(', ')}`);

        // 3. 执行实际的npm包版本更新操作
        Logger.log(`开始执行npm包升级...`);

        // 使用自定义应用列表执行升级
        const result = await mainWithCustomApps({
          npmName,
          npmVersion,
          branch,
          clientDir: finalClientDir,
        }, targetApps);

        // 4. 清理内存中的升级任务数据
        memoryStore.delete(latestUpgradeKey);
        memoryStore.delete('latest_npm_upgrade');

        Logger.log(`npm包升级完成`);

        return {
          content: [
            {
              type: "text",
              text: `✅ npm包升级任务执行完成\n\n目标应用: ${targetApps.join(', ')}\n升级包: ${npmName}@${npmVersion}\n分支: ${branch}\n\n详细结果:\n${result}`,
            },
          ],
        };

      } catch (error: any) {
        Logger.error("npm_publish_upgrade_app_running 执行失败:", error);

        // 发生错误时不清理数据，允许用户重试
        return {
          content: [
            {
              type: "text",
              text: `❌ npm包升级执行失败: ${error.message}\n\n你可以重新尝试执行此工具，或者重新开始整个流程`,
            },
          ],
        };
      }
    }
  );
}
